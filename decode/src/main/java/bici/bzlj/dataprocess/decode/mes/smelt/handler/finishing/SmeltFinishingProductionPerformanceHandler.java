package bici.bzlj.dataprocess.decode.mes.smelt.handler.finishing;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import bici.bzlj.dataprocess.core.event.MessageEvent;
import bici.bzlj.dataprocess.core.utils.JsonUtils;
import bici.bzlj.dataprocess.decode.common.ForwardHandler;
import bici.bzlj.dataprocess.decode.mes.smelt.handler.finishing.common.DealJsonNode;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;

import java.util.*;
import java.util.stream.Collectors;


/**
 * TODO
 *  特冶精整投入物料电文监听
 *
 * <AUTHOR>
 * @date 2025/5/15 16:17
 */
@MessageHandler(messageType = {"DXMTR2","DYMTR2","DZMTR2"}, desc = "特冶精整投入物料电文监听")
@Slf4j
public class SmeltFinishingProductionPerformanceHandler extends ForwardHandler<String> {

    protected SmeltFinishingProductionPerformanceHandler(StreamBridge streamBridge) {
        super(streamBridge);
    }

    @Override
    public String getGyckServiceId() {
        return SERVICE_ID;
    }

    private static final String SERVICE_ID = "production_performance";

    /**
     * 消息处理
     *
     * @param event                消息事件
     * @param currentHandleContext 处理上下文
     */
    @Override
    public void handle(MessageEvent<String> event, Map<String, Object> currentHandleContext, Map<String, Object> handlesContext) {
        String convertData = event.getConvertData();
        JsonNode jsonNode = JsonUtils.toJsonNode(convertData);
        if (jsonNode instanceof ArrayNode) {
            ArrayNode arrayNode = (ArrayNode) jsonNode;

            // 先执行原有的modifyTaskCode逻辑
            arrayNode.forEach(DealJsonNode::modifyTaskCode);

            // 按照endTime进行升序排序并设置startTime
            sortByEndTimeAndSetStartTime(arrayNode);

        }else{
            DealJsonNode.modifyTaskCode(jsonNode);
        }
        event.setConvertData(JsonUtils.toJson(jsonNode));
        // 将处理结果放入上下文
        currentHandleContext.put("serviceId", SERVICE_ID);
        currentHandleContext.put(SERVICE_ID, event.getConvertData());
    }

    /**
     * 按照endTime进行升序排序，并设置前一个node的endTime为后一个node的startTime
     *
     * @param arrayNode 需要排序的ArrayNode
     */
    private void sortByEndTimeAndSetStartTime(ArrayNode arrayNode) {
        if (arrayNode == null || arrayNode.size() <= 1) {
            return;
        }

        // 将ArrayNode转换为List进行排序
        List<JsonNode> nodeList = new ArrayList<>();
        arrayNode.forEach(nodeList::add);

        // 按照endTime进行升序排序
        nodeList.sort((node1, node2) -> {
            JsonNode endTime1 = node1.get("endTime");
            JsonNode endTime2 = node2.get("endTime");

            // 如果endTime字段不存在或为null，则保持原有顺序
            if (endTime1 == null || endTime1.isNull() || endTime2 == null || endTime2.isNull()) {
                return 0;
            }

            String endTimeStr1 = endTime1.asText();
            String endTimeStr2 = endTime2.asText();

            // 按字符串进行比较（假设时间格式是可比较的字符串格式）
            return endTimeStr1.compareTo(endTimeStr2);
        });

        // 清空原ArrayNode并重新添加排序后的节点
        arrayNode.removeAll();
        nodeList.forEach(arrayNode::add);

        // 设置前一个node的endTime为后一个node的startTime
        for (int i = 0; i < nodeList.size() - 1; i++) {
            JsonNode currentNode = nodeList.get(i);
            JsonNode nextNode = nodeList.get(i + 1);

            JsonNode nextStartTime = nextNode.get("startTime");
            if (nextStartTime != null && !nextStartTime.isNull() && currentNode instanceof ObjectNode) {
                // 将下一个节点的startTime设置为当前节点的endTime
                ((ObjectNode) currentNode).put("endTime", nextStartTime.asText());
            }
        }
    }

}
